{
   "compilerOptions": {
      // Environment setup & latest features
      "lib": ["ESNext", "DOM", "DOM.Iterable"],
      "target": "ESNext",
      "module": "ESNext",
      "moduleDetection": "force",
      "jsx": "react-jsx",
      "jsxImportSource": "retend",
      "allowJs": true,

      "outDir": "./dist",
      "resolvePackageJsonImports": true,

      // Bundler mode
      "moduleResolution": "bundler",
      "allowImportingTsExtensions": true,
      "verbatimModuleSyntax": true,
      "noEmit": true,

      // Best practices
      "strict": true,
      "skipLibCheck": true,
      "noFallthroughCasesInSwitch": true,

      // Some stricter flags (disabled by default)
      "noUnusedLocals": false,
      "noUnusedParameters": false,
      "noPropertyAccessFromIndexSignature": false,
      "declaration": false,
      "isolatedModules": true,
      "types": ["@cloudflare/workers-types/2023-07-01", "vite/client"],

      "paths": {
         "@/*": ["./*"]
      }
   },
   "include": ["./"]
}
