import type { JSX } from 'retend/jsx-runtime';
import styles from './floating-action-button.module.css';
import { useDerivedValue } from 'retend-utils/hooks';
import { Cell } from 'retend';

type ButtonProps = JSX.IntrinsicElements['button'];

/**
 * Props for the FloatingActionButton component.
 */
export interface FloatingActionButtonProps extends ButtonProps {
   /**
    * The vertical position of the button. Can be "top", "bottom", or "center". Defaults to "bottom".
    */
   block?: JSX.ValueOrCell<'top' | 'bottom' | 'center'>;
   /**
    * The horizontal position of the button. Can be "left", "right", or "center". Defaults to "center".
    */
   inline?: JSX.ValueOrCell<'left' | 'right' | 'center'>;
   /**
    * Whether the button should be outlined. Defaults to false.
    */
   outlined?: JSX.ValueOrCell<boolean>;
}

/**
 * A floating action button component.
 * It automatically styles svg icons placed inside it.
 *
 * @param props - The component props.
 * @returns A floating action button element.
 *
 * @example
 * ```tsx
 * <FloatingActionButton onClick={() => alert('Button clicked!')}>
 *   <Icon name="add" />
 * </FloatingActionButton>
 * ```
 */
export function FloatingActionButton(props: FloatingActionButtonProps) {
   const {
      children,
      block: blockProp = 'bottom',
      inline: inlineProp = 'center',
      outlined: outlinedProp = false,
      ...rest
   } = props;

   const block = useDerivedValue(blockProp);
   const inline = useDerivedValue(inlineProp);
   const isOutlined = useDerivedValue(outlinedProp);

   const blockClass = Cell.derived(() => styles[`block-${block.get()}`]);
   const inlineClass = Cell.derived(() => styles[`inline-${inline.get()}`]);

   return (
      <button
         {...rest}
         data-outlined={isOutlined}
         class={[
            styles.floatingActionButton,
            blockClass,
            inlineClass,
            rest.class,
         ]}
      >
         {props.children}
      </button>
   );
}
