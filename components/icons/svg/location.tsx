import type { IconProps } from '../index';

export default function Location(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 20 21'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Location</title>
         <path
            d='M15.8333 10.6785C15.8333 13.9002 13.2216 16.5119 9.99996 16.5119C6.7783 16.5119 4.16663 13.9002 4.16663 10.6785C4.16663 7.45689 6.7783 4.84521 9.99996 4.84521C13.2216 4.84521 15.8333 7.45689 15.8333 10.6785Z'
            stroke='currentColor'
            stroke-width='1.25'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M15.8334 10.6785H17.5'
            stroke='currentColor'
            stroke-width='1.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M2.5 10.6785H4.16667'
            stroke='currentColor'
            stroke-width='1.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M10 16.5117V18.1784'
            stroke='currentColor'
            stroke-width='1.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M10 3.17847V4.84513'
            stroke='currentColor'
            stroke-width='1.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M12.5 10.6785C12.5 12.0592 11.3807 13.1785 10 13.1785C8.61925 13.1785 7.5 12.0592 7.5 10.6785C7.5 9.29772 8.61925 8.17847 10 8.17847C11.3807 8.17847 12.5 9.29772 12.5 10.6785Z'
            stroke='currentColor'
            stroke-width='1.25'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
