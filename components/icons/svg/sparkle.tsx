import type { IconProps } from '../index';

export default function Sparkle(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Sparkle</title>
         <path
            d='M16.4343 4.35274L15.6315 11.8268L21.147 16.9341L13.673 16.1313L8.56571 21.6468L9.36848 14.1728L3.85295 9.0655L11.327 9.86827L16.4343 4.35274Z'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
