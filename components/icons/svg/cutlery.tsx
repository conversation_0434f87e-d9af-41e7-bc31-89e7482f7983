import type { IconProps } from '../index';

export default function Cutlery(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 38 39'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Cutlery</title>
         <path
            d='M28.0622 7.84033C26.9071 8.35381 25.9046 9.1582 25.153 10.1753L24.9938 10.3989C24.1663 11.6117 23.7224 13.0459 23.7214 14.5142V18.8403L23.7263 18.9243C23.7458 19.1193 23.8316 19.3031 23.9714 19.4429L24.0339 19.4995C24.1854 19.6236 24.3764 19.6916 24.5739 19.6919H28.0622V7.84033ZM29.8747 32.4683C29.8714 32.677 29.7973 32.8778 29.6657 33.0376L29.6061 33.103C29.4376 33.2716 29.2097 33.3679 28.9714 33.3716H28.9684C28.7582 33.3716 28.5555 33.2985 28.3942 33.1665L28.3268 33.106C28.1571 32.936 28.0622 32.7055 28.0622 32.4653V21.5054H24.5729C23.9552 21.504 23.36 21.2882 22.8864 20.8999L22.6911 20.7231C22.1919 20.2239 21.9105 19.5473 21.9089 18.8413V14.5142L21.9157 14.1597C21.9846 12.3871 22.5703 10.67 23.6042 9.22217L23.8161 8.93701C24.8299 7.62841 26.1816 6.62075 27.7253 6.02295L28.0593 5.90088L28.6599 5.68311V5.68408C28.7593 5.64691 28.8643 5.62727 28.9704 5.62842H28.9714L29.0602 5.63428C29.2658 5.65748 29.4586 5.74946 29.6061 5.89697L29.6657 5.96338C29.797 6.12307 29.8714 6.32328 29.8747 6.53174V32.4683Z'
            fill='currentColor'
            stroke='currentColor'
            stroke-width='0.439496'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M17.9807 5.64209C18.221 5.64209 18.4514 5.73782 18.6213 5.90771L18.6819 5.97412C18.814 6.13538 18.8869 6.33804 18.887 6.54834V15.4487C18.8869 16.3567 18.5491 17.2299 17.9426 17.8999L17.8176 18.0317C17.1325 18.7169 16.2026 19.1021 15.2336 19.1021H14.1624V32.4653C14.1623 32.7055 14.0675 32.936 13.8977 33.106C13.7277 33.276 13.4965 33.3716 13.2561 33.3716H13.2532C13.0445 33.3683 12.8436 33.2942 12.6838 33.1626L12.6184 33.103C12.4499 32.9345 12.3536 32.7066 12.3499 32.4683V19.1021H11.2776C10.3704 19.0994 9.49864 18.76 8.82935 18.1538L8.69849 18.0288C8.01406 17.3444 7.62794 16.4167 7.62524 15.4487V6.54834C7.62529 6.308 7.72092 6.07766 7.89087 5.90771L7.95728 5.84717C8.11851 5.71511 8.32126 5.64213 8.53149 5.64209C8.7719 5.64209 9.0031 5.73772 9.1731 5.90771L9.23267 5.97412C9.36482 6.13538 9.4377 6.33803 9.43774 6.54834V13.1685H12.3499V6.54834C12.3499 6.308 12.4455 6.07766 12.6155 5.90771C12.7854 5.73777 13.0158 5.64213 13.2561 5.64209C13.4965 5.64209 13.7277 5.73772 13.8977 5.90771C14.0675 6.07764 14.1623 6.30815 14.1624 6.54834V13.1685H17.0745V6.54834C17.0745 6.308 17.1701 6.07766 17.3401 5.90771L17.4065 5.84717C17.5677 5.71512 17.7705 5.64212 17.9807 5.64209ZM9.43774 15.4468L9.44849 15.6284C9.49295 16.0489 9.68012 16.444 9.98169 16.7456L10.1165 16.8677C10.4433 17.1359 10.8541 17.2855 11.2805 17.2886H15.2336C15.7217 17.2886 16.1903 17.0947 16.5354 16.7495L16.6575 16.6147C16.9258 16.2873 17.0744 15.8757 17.0745 15.4487V14.981H9.43774V15.4468Z'
            fill='currentColor'
            stroke='currentColor'
            stroke-width='0.439496'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
