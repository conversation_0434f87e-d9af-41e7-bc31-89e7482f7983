import type { IconProps } from '../index';

export default function CoinStack(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 22 23'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Coin Stack</title>
         <path
            d='M12.1353 5.31185C12.1353 6.28985 9.95508 7.08268 7.26554 7.08268C4.57603 7.08268 2.39575 6.28985 2.39575 5.31185M12.1353 5.31185C12.1353 4.33384 9.95508 3.54102 7.26554 3.54102C4.57603 3.54102 2.39575 4.33384 2.39575 5.31185M12.1353 5.31185V9.25828C11.0538 9.58308 10.3645 10.0744 10.3645 10.6243M2.39575 5.31185V15.9368C2.39575 16.9149 4.57603 17.7077 7.26554 17.7077C8.44282 17.7077 9.52256 17.5557 10.3645 17.303V10.6243M10.3645 10.6243C10.3645 11.6024 12.5448 12.3952 15.2343 12.3952C17.9238 12.3952 20.1041 11.6024 20.1041 10.6243M10.3645 10.6243C10.3645 9.64634 12.5448 8.85352 15.2343 8.85352C17.9238 8.85352 20.1041 9.64634 20.1041 10.6243M10.3645 10.6243V17.7077C10.3645 18.6857 12.5448 19.4785 15.2343 19.4785C17.9238 19.4785 20.1041 18.6857 20.1041 17.7077V10.6243M2.39575 8.85352C2.39575 9.83155 4.57603 10.6243 7.26554 10.6243C8.44282 10.6243 9.52256 10.4724 10.3645 10.2196M2.39575 12.3952C2.39575 13.3732 4.57603 14.166 7.26554 14.166C8.44282 14.166 9.52256 14.0141 10.3645 13.7613M20.1041 14.166C20.1041 15.144 17.9238 15.9368 15.2343 15.9368C12.5448 15.9368 10.3645 15.144 10.3645 14.166'
            stroke='currentColor'
            stroke-width='1.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
