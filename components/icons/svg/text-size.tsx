import type { IconProps } from '../index';

export default function TextSize(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Text Size</title>
         <g
            clip-path='url(#clip0_1291_10742)'
            xmlns='http://www.w3.org/2000/svg'
         >
            {' '}
            <path
               d='M17.1875 8.0625L19.0625 6.1875L20.9375 8.0625H17.1875ZM17.1875 17.4375L19.0625 19.3125L20.9375 17.4375H17.1875Z'
               fill='currentColor'
               xmlns='http://www.w3.org/2000/svg'
            />
            <path
               d='M19.0625 8.0625V17.4375M8.75 6.1875V19.3125M6.875 19.3125H10.625M17.1875 8.0625L19.0625 6.1875L20.9375 8.0625H17.1875ZM17.1875 17.4375L19.0625 19.3125L20.9375 17.4375H17.1875Z'
               stroke='currentColor'
               stroke-width='1.5'
               stroke-linecap='round'
               stroke-linejoin='round'
               xmlns='http://www.w3.org/2000/svg'
            />
            <path
               d='M13.4375 8.0625V6.1875H4.0625V8.0625'
               stroke='currentColor'
               stroke-width='1.5'
               stroke-linecap='round'
               stroke-linejoin='round'
               xmlns='http://www.w3.org/2000/svg'
            />
         </g>
         <defs xmlns='http://www.w3.org/2000/svg'>
            {' '}
            <clipPath id='clip0_1291_10742' xmlns='http://www.w3.org/2000/svg'>
               {' '}
               <rect
                  width='18.75'
                  height='18.75'
                  fill='white'
                  transform='translate(3.125 3.375)'
                  xmlns='http://www.w3.org/2000/svg'
               />
            </clipPath>
         </defs>
      </svg>
   );
}
