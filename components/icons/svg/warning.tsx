import type { IconProps } from '../index';

export default function Warning(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 50 51'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Warning</title>
         <path
            d='M6.25 19.9759V31.5242C6.25 32.6292 6.68898 33.689 7.4704 34.4704L16.2796 43.2796C17.061 44.061 18.1208 44.5 19.2259 44.5H30.7742C31.8792 44.5 32.939 44.061 33.7204 43.2796L42.5296 34.4704C43.311 33.689 43.75 32.6292 43.75 31.5242V19.9759C43.75 18.8708 43.311 17.811 42.5296 17.0296L33.7204 8.2204C32.939 7.43898 31.8792 7 30.7742 7H19.2259C18.1208 7 17.061 7.43898 16.2796 8.2204L7.4704 17.0296C6.68898 17.811 6.25 18.8708 6.25 19.9759Z'
            stroke='currentColor'
            stroke-width='3.02419'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M25 17.4165V27.8332'
            stroke='currentColor'
            stroke-width='3.02419'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M25 34.0834V34.0601'
            stroke='currentColor'
            stroke-width='3.02419'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
