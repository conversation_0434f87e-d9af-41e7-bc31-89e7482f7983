import type { IconProps } from '../index';

export default function Dollar(props: IconProps) {
   return (
      <svg
         {...props}
         viewBox='0 0 38 39'
         fill='none'
         xmlns='http://www.w3.org/2000/svg'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Dollar</title>
         <path
            xmlns='http://www.w3.org/2000/svg'
            d='M22.6562 14.8125C22.6562 14.8125 21.5013 13.25 18.7498 13.25C13.2812 13.25 13.2812 19.5 18.7498 19.5C24.2185 19.5 24.2187 25.75 18.7499 25.75C16.4062 25.75 14.8437 24.1875 14.8437 24.1875'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
         />
         <path
            xmlns='http://www.w3.org/2000/svg'
            d='M18.75 11.6875V27.3125'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
         />
         <path
            xmlns='http://www.w3.org/2000/svg'
            d='M32.8125 19.5C32.8125 27.2666 26.5166 33.5625 18.75 33.5625C10.9835 33.5625 4.6875 27.2666 4.6875 19.5C4.6875 11.7335 10.9835 5.4375 18.75 5.4375C26.5166 5.4375 32.8125 11.7335 32.8125 19.5Z'
            stroke='currentColor'
            stroke-width='2.25'
         />
      </svg>
   );
}
