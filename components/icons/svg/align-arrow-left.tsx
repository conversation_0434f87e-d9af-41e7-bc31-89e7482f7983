import type { IconProps } from '../index';

export default function AlignArrowLeft(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 17 16'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Align Arrow Left</title>
         <path
            d='M14.25 14.0723V2.44727'
            stroke='currentColor'
            stroke-width='1.29167'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M5.85413 8.25977H11.6666'
            stroke='currentColor'
            stroke-width='1.29167'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M6.99183 6.32227L5.96767 7.34643C5.46325 7.85082 5.46325 8.66871 5.96767 9.1731L6.99183 10.1973'
            stroke='currentColor'
            stroke-width='1.29167'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
