import type { IconProps } from '../index';

export default function ConnectedNodes(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 22 23'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Connected Nodes</title>
         <path
            d='M10.238 7.08268L5.17849 15.9368M5.93742 17.7077H16.5622M17.3213 15.9368L12.2619 7.08268M3.81242 19.4785H4.52075C5.01663 19.4785 5.26457 19.4785 5.45397 19.382C5.62057 19.2971 5.75602 19.1617 5.84092 18.9951C5.93742 18.8057 5.93742 18.5578 5.93742 18.0618V17.3535C5.93742 16.8576 5.93742 16.6097 5.84092 16.4203C5.75602 16.2537 5.62057 16.1183 5.45397 16.0334C5.26457 15.9368 5.01663 15.9368 4.52075 15.9368H3.81242C3.31654 15.9368 3.0686 15.9368 2.8792 16.0334C2.7126 16.1183 2.57715 16.2537 2.49225 16.4203C2.39575 16.6097 2.39575 16.8576 2.39575 17.3535V18.0618C2.39575 18.5578 2.39575 18.8057 2.49225 18.9951C2.57715 19.1617 2.7126 19.2971 2.8792 19.382C3.0686 19.4785 3.31654 19.4785 3.81242 19.4785ZM17.9791 19.4785H18.6874C19.1833 19.4785 19.4313 19.4785 19.6206 19.382C19.7873 19.2971 19.9227 19.1617 20.0076 18.9951C20.1041 18.8057 20.1041 18.5578 20.1041 18.0618V17.3535C20.1041 16.8576 20.1041 16.6097 20.0076 16.4203C19.9227 16.2537 19.7873 16.1183 19.6206 16.0334C19.4313 15.9368 19.1833 15.9368 18.6874 15.9368H17.9791C17.4832 15.9368 17.2352 15.9368 17.0459 16.0334C16.8792 16.1183 16.7438 16.2537 16.6589 16.4203C16.5624 16.6097 16.5624 16.8576 16.5624 17.3535V18.0618C16.5624 18.5578 16.5624 18.8057 16.6589 18.9951C16.7438 19.1617 16.8792 19.2971 17.0459 19.382C17.2352 19.4785 17.4832 19.4785 17.9791 19.4785ZM10.8958 7.08268H11.6041C12.1 7.08268 12.3479 7.08268 12.5373 6.98618C12.7039 6.90129 12.8393 6.76584 12.9242 6.59924C13.0208 6.40984 13.0208 6.16189 13.0208 5.66602V4.95768C13.0208 4.4618 13.0208 4.21386 12.9242 4.02446C12.8393 3.85786 12.7039 3.72241 12.5373 3.63752C12.3479 3.54102 12.1 3.54102 11.6041 3.54102H10.8958C10.3998 3.54102 10.1519 3.54102 9.96252 3.63752C9.79589 3.72241 9.66051 3.85786 9.5756 4.02446C9.47909 4.21386 9.47908 4.4618 9.47908 4.95768V5.66602C9.47908 6.16189 9.47909 6.40984 9.5756 6.59924C9.66051 6.76584 9.79589 6.90129 9.96252 6.98618C10.1519 7.08268 10.3998 7.08268 10.8958 7.08268Z'
            stroke='currentColor'
            stroke-width='1.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
