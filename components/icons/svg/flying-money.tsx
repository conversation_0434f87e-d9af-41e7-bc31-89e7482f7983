import type { IconProps } from '../index';

export default function FlyingMoney(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 22 23'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Flying Money</title>
         <g
            clip-path='url(#clip0_1580_7115)'
            xmlns='http://www.w3.org/2000/svg'
         >
            <path
               d='M21.2109 5.5332L1.28906 5.5332C1.11294 5.5332 0.944035 5.60317 0.819499 5.7277C0.694964 5.85224 0.625 6.02115 0.625 6.19727L0.625 16.8223C0.625 16.9984 0.694964 17.1673 0.819499 17.2918C0.944035 17.4164 1.11294 17.4863 1.28906 17.4863H21.2109C21.3871 17.4863 21.556 17.4164 21.6805 17.2918C21.805 17.1673 21.875 16.9984 21.875 16.8223V6.19727C21.875 6.02115 21.805 5.85224 21.6805 5.7277C21.556 5.60317 21.3871 5.5332 21.2109 5.5332ZM17.2863 16.1582H5.21367C5.07527 15.3438 4.68714 14.5925 4.10299 14.0083C3.51884 13.4242 2.76756 13.0361 1.95312 12.8977V10.1219C2.76756 9.98347 3.51884 9.59534 4.10299 9.01119C4.68714 8.42704 5.07527 7.67576 5.21367 6.86133H17.2863C17.4247 7.67576 17.8129 8.42704 18.397 9.01119C18.9812 9.59534 19.7324 9.98347 20.5469 10.1219V12.8977C19.7324 13.0361 18.9812 13.4242 18.397 14.0083C17.8129 14.5925 17.4247 15.3438 17.2863 16.1582ZM20.5469 8.76055C20.0901 8.64131 19.6733 8.40249 19.3395 8.06867C19.0057 7.73486 18.7669 7.31811 18.6477 6.86133H20.5469V8.76055ZM3.85234 6.86133C3.73311 7.31811 3.49429 7.73486 3.16047 8.06867C2.82665 8.40249 2.40991 8.64131 1.95312 8.76055V6.86133H3.85234ZM1.95312 14.259C2.40991 14.3782 2.82665 14.617 3.16047 14.9509C3.49429 15.2847 3.73311 15.7014 3.85234 16.1582H1.95312V14.259ZM18.6477 16.1582C18.7669 15.7014 19.0057 15.2847 19.3395 14.9509C19.6733 14.617 20.0901 14.3782 20.5469 14.259V16.1582H18.6477Z'
               fill='currentColor'
               xmlns='http://www.w3.org/2000/svg'
            />
            <path
               d='M5.36267 1.30371C5.40358 1.30913 5.44389 1.31788 5.48279 1.33105L5.59607 1.38281L9.54138 3.6543C9.68718 3.73825 9.7934 3.87663 9.83728 4.03906C9.8812 4.20168 9.85871 4.37545 9.77478 4.52148C9.69082 4.66727 9.55244 4.77352 9.39001 4.81738C9.22741 4.86129 9.05363 4.83881 8.90759 4.75488H8.90857L5.54138 2.80957L5.51404 2.79395L5.49841 2.82129L4.50232 4.52051V4.52148C4.41839 4.66726 4.27994 4.77348 4.11755 4.81738C3.95494 4.8613 3.78117 4.83881 3.63513 4.75488C3.48928 4.67093 3.38214 4.5326 3.33826 4.37012C3.29447 4.20778 3.31715 4.03453 3.40076 3.88867L4.72888 1.61035C4.7927 1.50216 4.887 1.41626 4.99841 1.3623L5.1156 1.31934C5.19593 1.29778 5.28021 1.29286 5.36267 1.30371Z'
               fill='currentColor'
               stroke='currentColor'
               stroke-width='0.0625'
               stroke-linecap='round'
               xmlns='http://www.w3.org/2000/svg'
            />
            <path
               d='M18.3821 18.2275C18.5446 18.1836 18.7185 18.2062 18.8645 18.29C19.0105 18.374 19.1175 18.5132 19.1614 18.6758C19.1943 18.7978 19.1894 18.9259 19.1497 19.0439L19.0989 19.1572L17.7708 21.4346C17.7069 21.5429 17.6128 21.6296 17.5012 21.6836L17.387 21.7256C17.3601 21.7301 17.333 21.7324 17.3059 21.7324L17.2249 21.7256H17.22C17.1364 21.7247 17.0542 21.7065 16.9778 21.6738L16.9036 21.6367L12.9719 19.3916C12.826 19.3077 12.7189 19.1693 12.675 19.0068C12.6311 18.8442 12.6536 18.6704 12.7375 18.5244C12.8215 18.3784 12.9607 18.2715 13.1233 18.2275C13.2453 18.1947 13.3735 18.1995 13.4915 18.2393L13.6047 18.29L16.9719 20.2363L16.9993 20.252L17.0149 20.2246L17.9973 18.5244C18.0812 18.3785 18.2196 18.2715 18.3821 18.2275Z'
               fill='currentColor'
               stroke='currentColor'
               stroke-width='0.0625'
               stroke-linecap='round'
               xmlns='http://www.w3.org/2000/svg'
            />
            <path
               d='M11.25 7.52539C10.462 7.52539 9.69163 7.75907 9.0364 8.19688C8.38117 8.63469 7.87049 9.25696 7.56892 9.98501C7.26735 10.7131 7.18845 11.5142 7.34219 12.2871C7.49592 13.06 7.8754 13.7699 8.43262 14.3271C8.98985 14.8844 9.6998 15.2638 10.4727 15.4176C11.2456 15.5713 12.0467 15.4924 12.7748 15.1908C13.5028 14.8893 14.1251 14.3786 14.5629 13.7234C15.0007 13.0681 15.2344 12.2978 15.2344 11.5098C15.2344 10.453 14.8146 9.4396 14.0674 8.69239C13.3202 7.94517 12.3067 7.52539 11.25 7.52539ZM11.25 14.166C10.7246 14.166 10.2111 14.0102 9.77427 13.7184C9.33745 13.4265 8.99699 13.0116 8.79595 12.5263C8.5949 12.0409 8.5423 11.5068 8.64479 10.9916C8.74728 10.4763 9.00027 10.003 9.37175 9.63151C9.74323 9.26003 10.2165 9.00705 10.7318 8.90455C11.2471 8.80206 11.7811 8.85467 12.2665 9.05571C12.7519 9.25676 13.1667 9.59721 13.4586 10.034C13.7505 10.4709 13.9063 10.9844 13.9063 11.5098C13.9063 12.2142 13.6264 12.8899 13.1283 13.388C12.6301 13.8862 11.9545 14.166 11.25 14.166Z'
               fill='currentColor'
               xmlns='http://www.w3.org/2000/svg'
            />
         </g>
         <defs xmlns='http://www.w3.org/2000/svg'>
            <clipPath id='clip0_1580_7115' xmlns='http://www.w3.org/2000/svg'>
               <rect
                  width='21.25'
                  height='21.25'
                  fill='white'
                  transform='translate(0.625 0.884766)'
                  xmlns='http://www.w3.org/2000/svg'
               />
            </clipPath>
         </defs>
      </svg>
   );
}
