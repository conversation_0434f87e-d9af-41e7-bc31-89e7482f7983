import type { IconProps } from '../index';

export default function Settings(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Settings</title>
         <path
            d='M10.6823 4.86221C10.2528 5.89293 9.04851 6.35857 8.03467 5.89064C6.35719 5.11642 4.61679 6.85683 5.391 8.53431C5.85893 9.54814 5.3933 10.7525 4.36258 11.1819C2.74682 11.8552 2.74682 14.1441 4.36258 14.8174C5.3933 15.2468 5.85893 16.4512 5.391 17.465C4.61679 19.1425 6.35719 20.8829 8.03467 20.1086C9.04851 19.6407 10.2528 20.1063 10.6823 21.137C11.3555 22.7529 13.6445 22.7529 14.3177 21.137C14.7472 20.1063 15.9516 19.6407 16.9653 20.1086C18.6428 20.8829 20.3832 19.1425 19.609 17.465C19.1411 16.4512 19.6067 15.2468 20.6374 14.8174C22.2532 14.1441 22.2532 11.8552 20.6374 11.1819C19.6067 10.7525 19.1411 9.54814 19.609 8.53431C20.3832 6.85683 18.6428 5.11642 16.9653 5.89064C15.9516 6.35857 14.7472 5.89293 14.3177 4.86221C13.6445 3.24645 11.3555 3.24645 10.6823 4.86221Z'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M15.625 13C15.625 14.7259 14.2259 16.125 12.5 16.125C10.7741 16.125 9.375 14.7259 9.375 13C9.375 11.2741 10.7741 9.875 12.5 9.875C14.2259 9.875 15.625 11.2741 15.625 13Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
