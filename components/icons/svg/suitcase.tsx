import type { IconProps } from '../index';

export default function Suitcase(props: IconProps) {
   return (
      <svg
         {...props}
         viewBox='0 0 38 39'
         fill='none'
         xmlns='http://www.w3.org/2000/svg'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Suitcase</title>
         <path
            d='M4.54541 16.3432C4.54541 14.8725 4.54541 14.1371 4.78569 13.557C5.10606 12.7836 5.72057 12.1691 6.49401 11.8487C7.0741 11.6084 7.80949 11.6084 9.28026 11.6084H28.2197C29.6905 11.6084 30.4258 11.6084 31.006 11.8487C31.7793 12.1691 32.3939 12.7836 32.7143 13.557C32.9545 14.1371 32.9545 14.8725 32.9545 16.3432V27.3912C32.9545 30.3672 32.9545 31.8552 32.0299 32.7798C31.1054 33.7044 29.6174 33.7044 26.6414 33.7044H25.0631H12.4368H10.8585C7.8825 33.7044 6.39448 33.7044 5.46995 32.7798C4.54541 31.8552 4.54541 30.3672 4.54541 27.3912V16.3432Z'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linejoin='round'
         />
         <path
            d='M12.4368 11.6085V8.45198C12.4368 6.70865 13.85 5.29541 15.5933 5.29541H21.9065C23.6498 5.29541 25.063 6.70865 25.063 8.45198V11.6085'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linejoin='round'
         />
         <path
            d='M4.54541 16.3435C4.83919 18.4215 5.70543 20.3426 7.00056 21.9802C9.58742 25.2513 13.8853 27.3915 18.75 27.3915C23.6145 27.3915 27.9125 25.2513 30.4993 21.9802C31.7945 20.3426 32.6608 18.4215 32.9545 16.3435'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
         />
         <path
            d='M17.9608 21.0781H19.5391'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
         />
      </svg>
   );
}
