import type { IconProps } from '../index';

export default function <PERSON>lle<PERSON>(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Pallete</title>
         <path
            d='M3.125 7.79167C3.125 5.82748 3.125 4.84539 3.7352 4.2352C4.34539 3.625 5.32748 3.625 7.29167 3.625C9.25585 3.625 10.2379 3.625 10.8481 4.2352C11.4583 4.84539 11.4583 5.82748 11.4583 7.79167V13V18.2083C11.4583 20.1725 11.4583 21.1546 10.8481 21.7648C10.2379 22.375 9.25585 22.375 7.29167 22.375C5.32748 22.375 4.34539 22.375 3.7352 21.7648C3.125 21.1546 3.125 20.1725 3.125 18.2083V13V7.79167Z'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M11.4584 8.31283L13.1996 6.57161C14.5885 5.18272 15.283 4.48828 16.1459 4.48828C17.0088 4.48828 17.7033 5.18272 19.0921 6.57161L19.9705 7.44989C21.3593 8.83878 22.0538 9.53322 22.0538 10.3962C22.0538 11.2591 21.3593 11.9536 19.9705 13.3424L11.4584 21.8545'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M7.29163 22.3753H17.7083C19.6725 22.3753 20.6545 22.3753 21.2648 21.7651C21.875 21.1549 21.875 20.1728 21.875 18.2087V16.6462C21.875 16.1621 21.875 15.9201 21.835 15.7189C21.6706 14.8924 21.0245 14.2464 20.1981 14.082C19.9968 14.042 19.7549 14.042 19.2708 14.042'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M7.29163 18.2184V18.208'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
