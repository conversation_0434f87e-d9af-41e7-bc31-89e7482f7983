import type { IconProps } from '../index';

export default function Cart(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 38 39'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Cart</title>
         <path
            d='M8.75723 18.6279C9.05106 21.2726 9.19798 22.5948 10.0879 23.3913C10.9777 24.1877 12.3082 24.1877 14.969 24.1877H15.1654H21.0478H23.1672C25.0003 24.1877 25.9167 24.1877 26.6605 23.7399C27.4042 23.2921 27.8331 22.4821 28.6908 20.8621L32.5473 13.5775C33.3765 12.0112 32.2411 10.1252 30.4687 10.1252H15.1654H14.7954C11.5365 10.1252 9.90703 10.1252 8.9754 11.1661C8.04378 12.207 8.22373 13.8265 8.58362 17.0654L8.75723 18.6279Z'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M4.6875 5.43774H5.46875C6.50656 5.43774 7.37841 6.21809 7.49302 7.24954L8.68059 17.9377'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M14.0625 31.219C14.0625 32.5134 13.0132 33.5627 11.7188 33.5627C10.4243 33.5627 9.375 32.5134 9.375 31.219C9.375 29.9246 10.4243 28.8752 11.7188 28.8752C13.0132 28.8752 14.0625 29.9246 14.0625 31.219Z'
            stroke='currentColor'
            stroke-width='2.25'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M28.125 31.219C28.125 32.5134 27.0756 33.5627 25.7812 33.5627C24.4869 33.5627 23.4375 32.5134 23.4375 31.219C23.4375 29.9246 24.4869 28.8752 25.7812 28.8752C27.0756 28.8752 28.125 29.9246 28.125 31.219Z'
            stroke='currentColor'
            stroke-width='2.25'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
