import type { IconProps } from '../index';

export default function Heart(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Heart</title>
         <path
            d='M5.08583 14.2458L10.9397 20.8619C11.7689 21.799 13.2311 21.799 14.0603 20.8619L19.9142 14.2458C21.6568 12.2763 22.5621 9.79939 21.2528 7.43748C19.7401 4.70858 16.9682 4.15083 14.5595 5.9834C13.7312 6.61346 13.0515 7.33297 12.7195 7.70627C12.6042 7.83599 12.3958 7.83599 12.2805 7.70627C11.9485 7.33297 11.2687 6.61346 10.4405 5.9834C8.0318 4.15083 5.25984 4.70858 3.74716 7.43748C2.43791 9.79939 3.3432 12.2763 5.08583 14.2458Z'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
