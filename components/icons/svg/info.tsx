import type { IconProps } from '../index';

export default function Info(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 12 12'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Info</title>
         <path
            d='M0.999882 5.75043C0.999882 3.16019 3.09965 1.06043 5.68988 1.06043C8.2801 1.06043 10.3799 3.16019 10.3799 5.75043C10.3799 8.34064 8.2801 10.4404 5.68988 10.4404C3.09965 10.4404 0.999882 8.34064 0.999882 5.75043Z'
            stroke='currentColor'
            stroke-width='0.85'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M5.68994 7.83496V5.22941'
            stroke='currentColor'
            stroke-width='0.85'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M5.68994 3.66604V3.67188'
            stroke='currentColor'
            stroke-linecap='round'
            stroke-width='0.85'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
