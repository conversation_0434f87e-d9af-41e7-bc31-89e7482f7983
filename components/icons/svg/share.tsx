import type { IconProps } from '../index';

export default function Share(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Share</title>
         <path
            d='M21.875 7C21.875 8.72589 20.4759 10.125 18.75 10.125C17.0241 10.125 15.625 8.72589 15.625 7C15.625 5.27411 17.0241 3.875 18.75 3.875C20.4759 3.875 21.875 5.27411 21.875 7Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M21.875 19.5C21.875 21.2259 20.4759 22.625 18.75 22.625C17.0241 22.625 15.625 21.2259 15.625 19.5C15.625 17.7741 17.0241 16.375 18.75 16.375C20.4759 16.375 21.875 17.7741 21.875 19.5Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M9.375 13.25C9.375 14.9759 7.97589 16.375 6.25 16.375C4.52411 16.375 3.125 14.9759 3.125 13.25C3.125 11.5241 4.52411 10.125 6.25 10.125C7.97589 10.125 9.375 11.5241 9.375 13.25Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M9.08398 11.833L15.6251 8.5625'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M9.06885 14.6592L15.625 17.9373'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
