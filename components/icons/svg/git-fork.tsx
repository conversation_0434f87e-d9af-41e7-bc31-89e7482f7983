import type { IconProps } from '../index';

export default function GitFork(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Git Fork</title>
         <path
            d='M6.25 3.875C7.97589 3.875 9.375 5.27411 9.375 7C9.375 8.72589 7.97589 10.125 6.25 10.125C4.52411 10.125 3.125 8.72589 3.125 7C3.125 5.27411 4.52411 3.875 6.25 3.875Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M18.75 3.875C20.4759 3.875 21.875 5.27411 21.875 7C21.875 8.72589 20.4759 10.125 18.75 10.125C17.0241 10.125 15.625 8.72589 15.625 7C15.625 5.27411 17.0241 3.875 18.75 3.875Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M12.5 16.375C14.2259 16.375 15.625 17.7741 15.625 19.5C15.625 21.2259 14.2259 22.625 12.5 22.625C10.7741 22.625 9.375 21.2259 9.375 19.5C9.375 17.7741 10.7741 16.375 12.5 16.375Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M6.2627 10.125C6.36767 12.6777 7.16207 13.25 10.0542 13.25H14.9458C17.8379 13.25 18.6323 12.6777 18.7373 10.125'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M12.5 16.375V13.25'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
