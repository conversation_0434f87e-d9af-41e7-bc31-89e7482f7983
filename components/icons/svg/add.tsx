import type { IconProps } from '../index';

export default function Add(props: IconProps) {
   return (
      <svg
         {...props}
         viewBox='0 0 39 39'
         fill='none'
         xmlns='http://www.w3.org/2000/svg'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Icon</title>
         <path
            d='M6 19.4299H19.5M19.5 19.4299H33M19.5 19.4299V32.9299M19.5 19.4299V5.92993'
            stroke='currentColor'
            stroke-width='2.7'
            stroke-linecap='round'
            stroke-linejoin='round'
         />
      </svg>
   );
}
