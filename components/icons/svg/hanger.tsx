import type { IconProps } from '../index';

export default function Hanger(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 38 38'
         fill='none'
      >
         <title>Hanger</title>
         <path
            d='M15.1498 6.35795C15.1498 4.71052 16.6356 3.375 18.4686 3.375C20.3014 3.375 21.7873 4.71052 21.7873 6.35795C21.7873 7.13331 21.5072 7.83958 21.0195 8.37003C20.0856 9.38603 18.6898 10.4093 18.6898 11.7273V12.1717M18.6898 12.1717C19.8328 12.1584 20.9803 12.4759 21.9445 13.1259L33.5578 20.9545C35.5991 22.3305 34.5164 25.25 31.9648 25.25H28.375M18.6898 12.1717C17.5562 12.1849 16.4272 12.5234 15.4859 13.1855L4.39892 20.9844C2.41128 22.3825 3.51127 25.25 6.03522 25.25H9.625'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M9.625 28.375C9.625 25.4288 9.625 23.9556 10.5403 23.0403C11.4556 22.125 12.9287 22.125 15.875 22.125H22.125C25.0712 22.125 26.5444 22.125 27.4597 23.0403C28.375 23.9556 28.375 25.4288 28.375 28.375C28.375 31.3212 28.375 32.7944 27.4597 33.7097C26.5444 34.625 25.0712 34.625 22.125 34.625H15.875C12.9287 34.625 11.4556 34.625 10.5403 33.7097C9.625 32.7944 9.625 31.3212 9.625 28.375Z'
            stroke='currentColor'
            stroke-width='2.25'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
