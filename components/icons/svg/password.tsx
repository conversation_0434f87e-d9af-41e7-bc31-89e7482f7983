import type { IconProps } from '../index';

export default function Password(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 25'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Password</title>
         <path
            d='M11.4792 20.3125H7.81258C7.16675 20.3125 6.59383 20.2917 6.08341 20.2187C3.34383 19.9167 2.60425 18.625 2.60425 15.1042V9.89583C2.60425 6.375 3.34383 5.08333 6.08341 4.78125C6.59383 4.70833 7.16675 4.6875 7.81258 4.6875H11.4167'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M15.6458 4.6875H17.1874C17.8333 4.6875 18.4062 4.70833 18.9166 4.78125C21.6562 5.08333 22.3958 6.375 22.3958 9.89583V15.1042C22.3958 18.625 21.6562 19.9167 18.9166 20.2187C18.4062 20.2917 17.8333 20.3125 17.1874 20.3125H15.6458'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M15.625 2.08301V22.9163'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M11.5569 12.5H11.5663'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M7.39014 12.5H7.39949'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
