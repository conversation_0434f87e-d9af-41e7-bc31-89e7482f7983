import type { IconProps } from '../index';

export default function Paper(props: IconProps) {
   return (
      <svg
         {...props}
         viewBox='0 0 38 39'
         fill='none'
         xmlns='http://www.w3.org/2000/svg'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Paper</title>
         <path
            xmlns='http://www.w3.org/2000/svg'
            d='M13.068 18.5529H12.121V20.4469H13.068V18.5529ZM24.432 20.4469H25.379V18.5529H24.432V20.4469ZM13.068 12.8709H12.121V14.7649H13.068V12.8709ZM16.856 14.7649H17.803V12.8709H16.856V14.7649ZM24.432 6.24192L25.1017 5.57229L24.8243 5.29492H24.432V6.24192ZM30.114 11.9239H31.061V11.5317L30.7837 11.2543L30.114 11.9239ZM18.75 27.0759L19.1735 27.9229L19.2499 27.8848L19.3182 27.8335L18.75 27.0759ZM13.068 20.4469H24.432V18.5529H13.068V20.4469ZM13.068 14.7649H16.856V12.8709H13.068V14.7649ZM28.22 31.8109H9.28003V33.7049H28.22V31.8109ZM8.33303 30.8639V8.13592H6.43903V30.8639H8.33303ZM9.28003 7.18892H24.432V5.29492H9.28003V7.18892ZM29.167 11.9239V30.8639H31.061V11.9239H29.167ZM23.7623 6.91155L29.4443 12.5935L30.7837 11.2543L25.1017 5.57229L23.7623 6.91155ZM9.28003 31.8109C8.75702 31.8109 8.33303 31.3869 8.33303 30.8639H6.43903C6.43903 32.4329 7.71098 33.7049 9.28003 33.7049V31.8109ZM28.22 33.7049C29.789 33.7049 31.061 32.4329 31.061 30.8639H29.167C29.167 31.3869 28.743 31.8109 28.22 31.8109V33.7049ZM8.33303 8.13592C8.33303 7.61291 8.75702 7.18892 9.28003 7.18892V5.29492C7.71098 5.29492 6.43903 6.56688 6.43903 8.13592H8.33303ZM14.9134 27.3754C15.1239 26.7439 15.7213 26.16 16.4862 25.9545C17.1994 25.7628 18.1175 25.8886 19.0274 26.7986L20.3666 25.4592C19.0038 24.0964 17.3965 23.7487 15.9948 24.1253C14.6447 24.488 13.5375 25.5139 13.1166 26.7765L14.9134 27.3754ZM19.0274 26.7986C19.0892 26.8604 19.1377 26.9157 19.1754 26.9644L20.671 25.8022C20.5798 25.6848 20.478 25.5706 20.3666 25.4592L19.0274 26.7986ZM19.1754 26.9644C19.3237 27.1551 19.2906 27.2146 19.2954 27.1737C19.2987 27.146 19.3076 27.1888 19.1937 27.2924C19.0789 27.3968 18.8928 27.5112 18.6449 27.6117C18.4021 27.7104 18.1378 27.7803 17.8985 27.8165C17.6456 27.8547 17.4831 27.846 17.4249 27.8345C17.3922 27.828 17.4588 27.8337 17.5553 27.9047C17.678 27.9949 17.8317 28.185 17.8439 28.4589C17.849 28.5745 17.8258 28.6646 17.8051 28.7203C17.785 28.7745 17.7643 28.8042 17.7614 28.8084C17.7584 28.8125 17.7897 28.7695 17.8983 28.6817C18.1139 28.5074 18.5109 28.2542 19.1735 27.9229L18.3265 26.2289C17.6119 26.5861 17.0768 26.9104 16.7076 27.2089C16.5237 27.3576 16.3495 27.5248 16.2156 27.7138C16.0879 27.8941 15.9356 28.1816 15.9518 28.5432C15.97 28.9525 16.1921 29.2533 16.4333 29.4307C16.6485 29.5889 16.8838 29.6582 17.0578 29.6925C17.4129 29.7628 17.8156 29.7446 18.1819 29.6893C18.9024 29.5802 19.834 29.2701 20.4679 28.6936C20.7982 28.3932 21.1098 27.9612 21.1764 27.3949C21.2447 26.8151 21.0357 26.2715 20.671 25.8022L19.1754 26.9644ZM19.3182 27.8335C19.6661 27.5725 19.9932 27.3848 20.2993 27.2524L19.5472 25.5141C19.0924 25.7109 18.6374 25.9766 18.1818 26.3183L19.3182 27.8335ZM20.2993 27.2524C21.5321 26.7189 22.6313 27.0009 23.7585 27.5377C24.0415 27.6725 24.318 27.8195 24.5981 27.971C24.8711 28.1188 25.1593 28.2777 25.4336 28.4165C25.9544 28.6801 26.6035 28.9699 27.273 28.9699V27.0759C27.0959 27.0759 26.8039 26.9873 26.2889 26.7267C26.0453 26.6034 25.7921 26.4638 25.4999 26.3054C25.2146 26.1511 24.9023 25.9846 24.5729 25.8278C23.2502 25.1978 21.521 24.6601 19.5472 25.5141L20.2993 27.2524Z'
            fill='currentColor'
         />
      </svg>
   );
}
