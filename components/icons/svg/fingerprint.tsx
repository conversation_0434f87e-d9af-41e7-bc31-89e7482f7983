import type { IconProps } from '../index';

export default function Fingerprint(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 25'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Fingerprint</title>
         <path
            d='M18.393 5.20833C16.7829 3.90542 14.7326 3.125 12.5 3.125C7.32233 3.125 3.125 7.32233 3.125 12.5C3.125 15.4451 3.79903 18.0729 4.38031 19.7917'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M21.3416 9.375C21.687 10.3524 21.875 11.4043 21.875 12.5C21.875 13.5957 21.3416 14.387 21.3416 15.625C21.3416 16.2628 21.4831 17.0728 21.6204 17.7083'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M9.375 8.33126C9.89176 7.94359 10.4898 7.64623 11.152 7.4688C13.9304 6.72431 16.7864 8.37318 17.5308 11.1516C18.2753 13.9301 17.1875 15.1038 17.1875 16.1455C17.1875 18.0751 18.2292 19.7913 18.75 20.833'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M9.37507 21.875C7.81257 18.75 6.7709 15.1042 7.29173 12.5'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M14.5834 21.875C13.0209 19.2708 10.9375 15.1042 13.0209 12.5'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
