import type { IconProps } from '../index';

export default function Alphabet(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Alphabet</title>
         <path
            d='M13.5417 19L8.33333 6.5L3.125 19M11.4583 14.8333H5.20833M21.875 19V15.875M21.875 15.875V12.75M21.875 15.875C21.875 17.6009 20.4759 19 18.75 19C17.0241 19 15.625 17.6009 15.625 15.875C15.625 14.1491 17.0241 12.75 18.75 12.75C20.4759 12.75 21.875 14.1491 21.875 15.875Z'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
