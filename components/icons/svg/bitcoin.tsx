import type { IconProps } from '../index';

export default function Bitcoin(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 38 38'
         fill='none'
      >
         <title>Bitcoin</title>
         <path
            d='M12.5 13.2812H21.0938C25 13.2812 25 18.75 21.0938 18.75H16.4062'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M16.4062 18.75H21.0938C25 18.75 25 24.2188 21.0938 24.2188H12.5'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M15.625 26.5625V10.9375'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M20.3125 13.2812V10.9375'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M20.3125 26.5625V24.2188'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M32.8125 18.75C32.8125 26.5166 26.5166 32.8125 18.75 32.8125C10.9835 32.8125 4.6875 26.5166 4.6875 18.75C4.6875 10.9835 10.9835 4.6875 18.75 4.6875C26.5166 4.6875 32.8125 10.9835 32.8125 18.75Z'
            stroke='currentColor'
            stroke-width='2.25'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
