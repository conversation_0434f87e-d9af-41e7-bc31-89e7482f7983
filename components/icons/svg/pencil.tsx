import type { IconProps } from '../index';

export default function Pencil(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 16 16'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Pencil</title>
         <path
            d='M12.9392 4.92949L4.29562 13.6076C4.04542 13.8588 3.70548 14 3.35094 14H2.23172C1.86212 14 1.5625 13.6978 1.5625 13.3282V12.1991C1.5625 11.8464 1.70225 11.5081 1.95115 11.2582L10.597 2.57787C12.6046 0.898138 14.6123 3.24975 12.9392 4.92949Z'
            stroke='currentColor'
            stroke-width='0.960001'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M9.76892 3.54004L12.0474 5.81854'
            stroke='currentColor'
            stroke-width='0.960001'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
