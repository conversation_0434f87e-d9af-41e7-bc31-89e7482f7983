import type { IconProps } from '../index';

export default function Wallet(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 26 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Wallet</title>
         <path
            d='M17.9383 15.4901H17.9477M20.5417 4.91797H7.20833C6.04156 4.91797 5.45817 4.91797 5.01252 5.14503C4.62051 5.34477 4.3018 5.66348 4.10207 6.05549C3.875 6.50114 3.875 7.08452 3.875 8.2513V18.2513C3.875 19.4181 3.875 20.0014 4.10207 20.4472C4.3018 20.8391 4.62051 21.1578 5.01252 21.3576C5.45817 21.5847 6.04155 21.5847 7.20833 21.5847H19.2917C20.4584 21.5847 21.0419 21.5847 21.4875 21.3576C21.8795 21.1578 22.1982 20.8391 22.3979 20.4472C22.625 20.0014 22.625 19.4181 22.625 18.2513V12.418C22.625 11.2512 22.625 10.6678 22.3979 10.2222C22.1982 9.83015 21.8795 9.51144 21.4875 9.3117C21.0419 9.08463 20.4584 9.08464 19.2917 9.08464H8.04167M18.4071 15.4901C18.4071 15.7489 18.1972 15.9588 17.9383 15.9588C17.6795 15.9588 17.4696 15.7489 17.4696 15.4901C17.4696 15.2312 17.6795 15.0213 17.9383 15.0213C18.1972 15.0213 18.4071 15.2312 18.4071 15.4901Z'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
