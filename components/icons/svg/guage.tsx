import type { IconProps } from '../index';

export default function Guage(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Guage</title>
         <path
            d='M21.875 12.75C21.875 17.9277 17.6777 22.125 12.5 22.125C7.32233 22.125 3.125 17.9277 3.125 12.75C3.125 7.57233 7.32233 3.375 12.5 3.375C17.6777 3.375 21.875 7.57233 21.875 12.75Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M7.29175 12.7503C7.29175 9.87385 9.6236 7.54199 12.5001 7.54199'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M13.5416 12.7497C13.5416 13.325 13.0752 13.7913 12.4999 13.7913C11.9246 13.7913 11.4583 13.325 11.4583 12.7497C11.4583 12.1744 11.9246 11.708 12.4999 11.708C13.0752 11.708 13.5416 12.1744 13.5416 12.7497Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M12.5 12.7497L16.6667 8.58301'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
