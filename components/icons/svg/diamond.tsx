import type { IconProps } from '../index';

export default function Diamond(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 38 39'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Diamond</title>
         <path
            d='M7.50885 9.66586C8.42327 8.35955 8.88047 7.70639 9.55883 7.35319C10.2372 7 11.0345 7 12.629 7H18.7499H24.8709C26.4654 7 27.2628 7 27.941 7.35319C28.6195 7.70639 29.0767 8.35955 29.991 9.66586L30.9242 10.9988C32.2099 12.8358 32.8529 13.7543 32.8224 14.7898C32.792 15.8252 32.096 16.7042 30.7042 18.4623L22.4609 28.875C21.3151 30.3223 20.7421 31.0461 20.0737 31.3586C19.2348 31.7509 18.2651 31.7509 17.4262 31.3586C16.7578 31.0461 16.1848 30.3223 15.039 28.875L6.79568 18.4623C5.40385 16.7042 4.70794 15.8252 4.67743 14.7898C4.64693 13.7543 5.28987 12.8358 6.57576 10.9988L7.50885 9.66586Z'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M14.0625 12.4688L13.2813 13.6406C12.8179 14.3357 12.8669 15.2527 13.4017 15.8945L16.4063 19.5'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
