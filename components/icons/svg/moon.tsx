import type { IconProps } from '../index';

export default function Moon(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 20 19'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Moon</title>
         <path
            d='M16.8021 12.2163C16.8427 12.124 16.7505 12.0304 16.6556 12.0647C14.0285 13.0135 11.0076 12.4536 9.02531 10.472C7.05226 8.49974 6.4911 5.49794 7.42302 2.87973C7.46147 2.77168 7.35483 2.66674 7.25016 2.71363C7.2431 2.7168 7.23604 2.71998 7.22899 2.72316C5.77941 3.37781 4.48948 4.48208 3.7319 5.8733C2.92203 7.36052 2.65493 9.08302 2.97636 10.7456C3.2978 12.4081 4.18776 13.9071 5.49369 14.9855C6.79962 16.064 8.44015 16.6548 10.1341 16.6566C11.5908 16.6567 13.0141 16.221 14.221 15.4056C15.3352 14.6529 16.2179 13.5125 16.7743 12.2786C16.7837 12.2579 16.7929 12.2371 16.8021 12.2163Z'
            stroke='currentColor'
            stroke-width='1.5'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
