import type { IconProps } from '../index';

export default function Trophy(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 18 18'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Trophy</title>
         <path
            d='M8.99999 11.125C6.65278 11.125 4.74999 9.22216 4.74999 6.87496V2.93977C4.74999 2.64663 4.74999 2.50006 4.79271 2.3827C4.86432 2.18594 5.01931 2.03096 5.21606 1.95935C5.33342 1.91663 5.48 1.91663 5.77313 1.91663H12.2269C12.52 1.91663 12.6665 1.91663 12.7839 1.95935C12.9807 2.03096 13.1357 2.18594 13.2073 2.3827C13.25 2.50006 13.25 2.64663 13.25 2.93977V6.87496C13.25 9.22216 11.3472 11.125 8.99999 11.125ZM8.99999 11.125V13.25M13.25 3.33329H15.0208C15.3508 3.33329 15.5159 3.33329 15.6461 3.38721C15.8196 3.4591 15.9575 3.597 16.0294 3.77056C16.0833 3.90073 16.0833 4.06575 16.0833 4.39579V4.74996C16.0833 5.40869 16.0833 5.73806 16.0109 6.00829C15.8144 6.7416 15.2416 7.3144 14.5083 7.51089C14.2381 7.58329 13.9087 7.58329 13.25 7.58329M4.74999 3.33329H2.97916C2.64912 3.33329 2.4841 3.33329 2.35392 3.38721C2.18036 3.4591 2.04246 3.597 1.97057 3.77056C1.91666 3.90073 1.91666 4.06575 1.91666 4.39579V4.74996C1.91666 5.40869 1.91666 5.73806 1.98906 6.00829C2.18555 6.7416 2.75835 7.3144 3.49166 7.51089C3.76189 7.58329 4.09126 7.58329 4.74999 7.58329M5.77313 16.0833H12.2269C12.4007 16.0833 12.5417 15.9423 12.5417 15.7685C12.5417 14.3776 11.4141 13.25 10.0231 13.25H7.97687C6.5859 13.25 5.45832 14.3776 5.45832 15.7685C5.45832 15.9423 5.59927 16.0833 5.77313 16.0833Z'
            stroke='currentColor'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
