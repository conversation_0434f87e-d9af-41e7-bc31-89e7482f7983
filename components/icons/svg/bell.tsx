import type { IconProps } from '../index';

export default function Bell(props: IconProps) {
   return (
      <svg
         {...props}
         viewBox='0 0 52 53'
         fill='none'
         xmlns='http://www.w3.org/2000/svg'
      >
         <title xmlns='http://www.w3.org/2000/svg'>Bell</title>
         <path
            xmlns='http://www.w3.org/2000/svg'
            d='M19.5 39.5C19.5 39.5 19.5 46 26.1005 46C32.7013 46 32.7013 39.5 32.7013 39.5'
            stroke='currentColor'
            stroke-width='3.5'
            stroke-linejoin='round'
         />
         <path
            xmlns='http://www.w3.org/2000/svg'
            d='M15.1588 17.1959L11.8336 34.3416C11.3149 37.0161 13.3634 39.5 16.0876 39.5H19.4998H32.4998H35.9121C38.6363 39.5 40.6849 37.0159 40.1662 34.3416L36.8408 17.1959C36.8358 17.1694 36.831 17.146 36.8249 17.1198C36.6735 16.4575 34.3632 7 25.9998 7C17.6366 7 15.3262 16.4575 15.1747 17.1198C15.1687 17.146 15.164 17.1694 15.1588 17.1959Z'
            stroke='currentColor'
            stroke-width='3.5'
            stroke-linejoin='round'
         />
         <path
            xmlns='http://www.w3.org/2000/svg'
            d='M43.3333 15.6667C42.25 11.3333 41.1667 9.16667 39 7'
            stroke='currentColor'
            stroke-width='3.5'
            stroke-linecap='round'
            stroke-linejoin='round'
         />
         <path
            xmlns='http://www.w3.org/2000/svg'
            d='M8.66663 15.6667C9.74996 11.3333 10.8333 9.16667 13 7'
            stroke='currentColor'
            stroke-width='3.5'
            stroke-linecap='round'
            stroke-linejoin='round'
         />
      </svg>
   );
}
