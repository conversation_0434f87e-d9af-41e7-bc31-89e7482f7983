import type { IconProps } from '../index';

export default function Profile(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 25 26'
         fill='none'
      >
         <title xmlns='http://www.w3.org/2000/svg'>profile</title>
         <rect
            x='2.5'
            y='2.75'
            width='20.24'
            height='20.24'
            rx='10.12'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <circle
            cx='12.62'
            cy='8.95646'
            r='1.8588'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M12.6204 13.1851C13.7996 13.1851 14.8369 13.5336 15.5609 14.0601C16.2857 14.5872 16.6526 15.2521 16.6526 15.9136C16.6526 16.5749 16.2855 17.239 15.5609 17.7661C14.8369 18.2927 13.7997 18.642 12.6204 18.6421C11.441 18.6421 10.4031 18.2927 9.67902 17.7661C8.95445 17.239 8.58726 16.5749 8.58722 15.9136C8.58722 15.2521 8.95421 14.5872 9.67902 14.0601C10.4031 13.5335 11.441 13.1851 12.6204 13.1851Z'
            stroke='currentColor'
            stroke-width='1.5'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
