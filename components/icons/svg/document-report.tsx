import type { IconProps } from '../index';

export default function DocumentReport(props: IconProps) {
   return (
      <svg
         {...props}
         xmlns='http://www.w3.org/2000/svg'
         viewBox='0 0 38 38'
         fill='none'
      >
         <title>Document Report</title>
         <path
            d='M23.4375 17.1875V26.5625'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M18.75 18.75V26.5625'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M14.0625 21.875V26.5625'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linecap='round'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
         <path
            d='M27.8569 10.6694C28.7602 11.5726 29.2117 12.0242 29.4497 12.5985C29.6875 13.1728 29.6875 13.8115 29.6875 15.0888V26.5625C29.6875 29.5087 29.6875 30.9819 28.7722 31.8972C27.8569 32.8125 26.3837 32.8125 23.4375 32.8125H14.0625C11.1162 32.8125 9.64308 32.8125 8.7278 31.8972C7.8125 30.9819 7.8125 29.5087 7.8125 26.5625V10.9375C7.8125 7.99122 7.8125 6.51808 8.7278 5.6028C9.64308 4.6875 11.1162 4.6875 14.0625 4.6875H19.2861C20.5634 4.6875 21.2022 4.6875 21.7764 4.92538C22.3508 5.16325 22.8023 5.61486 23.7056 6.51808L27.8569 10.6694Z'
            stroke='currentColor'
            stroke-width='2.25'
            stroke-linejoin='round'
            xmlns='http://www.w3.org/2000/svg'
         />
      </svg>
   );
}
