// This file is auto-generated by scripts/generate-icon-names.mjs
// Do not edit this file manually.

export type IconName =
   | 'add'
   | 'align-arrow-left'
   | 'alphabet'
   | 'arrows'
   | 'atom'
   | 'battery'
   | 'bell'
   | 'bigger-calendar'
   | 'bin'
   | 'bitcoin'
   | 'cake'
   | 'calendar'
   | 'car'
   | 'caret'
   | 'cart'
   | 'chart'
   | 'chat-bubble'
   | 'checkmark'
   | 'clipboard'
   | 'clock'
   | 'coin-stack'
   | 'connected-nodes'
   | 'credit-card'
   | 'cutlery'
   | 'database'
   | 'desktop'
   | 'diamond'
   | 'document'
   | 'document-report'
   | 'dollar'
   | 'export'
   | 'fingerprint'
   | 'flying-money'
   | 'funnel'
   | 'gift'
   | 'git-fork'
   | 'google'
   | 'grid'
   | 'guage'
   | 'hamburger'
   | 'hanger'
   | 'healthcare'
   | 'heart'
   | 'house'
   | 'info'
   | 'lightbulb'
   | 'loader'
   | 'location'
   | 'location-pin'
   | 'medal'
   | 'moon'
   | 'more'
   | 'muscle'
   | 'padlock'
   | 'pallete'
   | 'paper'
   | 'paperclip'
   | 'password'
   | 'pencil'
   | 'pie-chart'
   | 'profile'
   | 'purse'
   | 'recurring'
   | 'retry'
   | 'safe'
   | 'search'
   | 'settings'
   | 'share'
   | 'shield'
   | 'shirt'
   | 'sparkle'
   | 'suitcase'
   | 'sun'
   | 'text-size'
   | 'toggles'
   | 'trophy'
   | 'user'
   | 'wallet'
   | 'warning';

import type { JSX } from 'retend/jsx-runtime';
import { noHydrate } from 'retend-server/client';

type SvgProps = JSX.IntrinsicElements['svg'];

export interface IconProps extends SvgProps {}

export interface AsyncIconProps extends SvgProps {
   name: IconName;
   direction?: unknown;
}

export async function DynamicIcon(props: AsyncIconProps) {
   const { name, ...rest } = props;
   const iconModule = await import(`./svg/${name}.tsx`);
   return iconModule.default(rest) as JSX.Template;
}

export const Icon = noHydrate(DynamicIcon);

export function AllIcons() {
   return (
      <>
         <Icon name='add' />
         <Icon name='align-arrow-left' />
         <Icon name='alphabet' />
         <Icon name='arrows' />
         <Icon name='atom' />
         <Icon name='battery' />
         <Icon name='bell' />
         <Icon name='bigger-calendar' />
         <Icon name='bin' />
         <Icon name='bitcoin' />
         <Icon name='cake' />
         <Icon name='calendar' />
         <Icon name='car' />
         <Icon name='caret' />
         <Icon name='cart' />
         <Icon name='chart' />
         <Icon name='chat-bubble' />
         <Icon name='checkmark' />
         <Icon name='clipboard' />
         <Icon name='clock' />
         <Icon name='coin-stack' />
         <Icon name='connected-nodes' />
         <Icon name='credit-card' />
         <Icon name='cutlery' />
         <Icon name='database' />
         <Icon name='desktop' />
         <Icon name='diamond' />
         <Icon name='document' />
         <Icon name='document-report' />
         <Icon name='dollar' />
         <Icon name='export' />
         <Icon name='fingerprint' />
         <Icon name='flying-money' />
         <Icon name='funnel' />
         <Icon name='gift' />
         <Icon name='git-fork' />
         <Icon name='google' />
         <Icon name='grid' />
         <Icon name='guage' />
         <Icon name='hamburger' />
         <Icon name='hanger' />
         <Icon name='healthcare' />
         <Icon name='heart' />
         <Icon name='house' />
         <Icon name='info' />
         <Icon name='lightbulb' />
         <Icon name='loader' />
         <Icon name='location' />
         <Icon name='location-pin' />
         <Icon name='medal' />
         <Icon name='moon' />
         <Icon name='more' />
         <Icon name='muscle' />
         <Icon name='padlock' />
         <Icon name='pallete' />
         <Icon name='paper' />
         <Icon name='paperclip' />
         <Icon name='password' />
         <Icon name='pencil' />
         <Icon name='pie-chart' />
         <Icon name='profile' />
         <Icon name='purse' />
         <Icon name='recurring' />
         <Icon name='retry' />
         <Icon name='safe' />
         <Icon name='search' />
         <Icon name='settings' />
         <Icon name='share' />
         <Icon name='shield' />
         <Icon name='shirt' />
         <Icon name='sparkle' />
         <Icon name='suitcase' />
         <Icon name='sun' />
         <Icon name='text-size' />
         <Icon name='toggles' />
         <Icon name='trophy' />
         <Icon name='user' />
         <Icon name='wallet' />
         <Icon name='warning' />
      </>
   );
}
