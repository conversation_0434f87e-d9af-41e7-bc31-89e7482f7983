.tabSwitcherContainer {
   display: grid;
   overflow: scroll;
   scroll-behavior: smooth;

   animation-name: var(--animation-scrolling);
   animation-timing-function: linear;
   grid-template-rows: auto 1fr;
   scroll-snap-type: x mandatory;
   animation-timeline: scroll(self inline);

   scrollbar-width: none;
   &::-webkit-scrollbar {
      display: none;
   }

   @supports not (animation-timeline: scroll(self inline)) {
      animation-duration: 1000ms;
      animation-timing-function: linear;
   }
}

.header {
   position: sticky;
   top: 0;
   left: 0;
   display: grid;
   overflow-x: auto;
   scroll-behavior: smooth;
   grid-template-columns: auto auto 1fr;

   scrollbar-width: none;
   &::-webkit-scrollbar {
      display: none;
   }

   &::after {
      display: block;
      align-self: flex-end;
      width: 100%;
      height: 3px;
      background-color: var(--canvas-text);
      content: "";
      grid-area: 1 / 2;
      translate: calc(var(--scroll-unit) * (var(--tabs) - 1) * 100%);
   }
}

.tabButton {
   display: flex;
   justify-content: start;
   min-width: calc(var(--spacing) * 3.5);
   border: none;
   color: var(--canvas-text);
   background-color: transparent;
   transition-timing-function: ease-in-out;
   transition-duration: var(--default-speed);
   padding-inline: 0;
   grid-area: 1 / 2;

   &:active {
      scale: 1;
   }

   :not([data-active="true"]) {
      opacity: 0.6;
   }
}

.tabContentContainer {
   display: grid;
   width: 100%;
   min-height: 100%;
   max-height: 0;
   grid-template-columns: repeat(var(--tabs), 100%);
   grid-template-rows: 1fr;
}

.tabContent {
   display: flex;
   overflow: auto;
   align-items: flex-start;
   align-self: flex-start;
   width: 100%;
   height: 100%;
   max-height: 100%;
   content-visibility: auto;
   contain-intrinsic-width: 100%;
   contain-intrinsic-height: 100%;
   justify-items: flex-start;
   scroll-snap-align: start;
   justify-self: flex-start;
   scroll-snap-stop: always;

   scrollbar-width: none;
   &::-webkit-scrollbar {
      display: none;
   }

   &[aria-hidden="true"] {
      pointer-events: none;
   }
}
