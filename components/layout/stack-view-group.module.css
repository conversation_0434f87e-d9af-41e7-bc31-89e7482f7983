@property --stack-view-group-pull-progress {
   syntax: "<number>";
   initial-value: 0;
   inherits: true;
}

body {
   --stack-view-slide: calc(var(--default-speed) * 3.5);
}

/* ---NAVIGATION STACK--- */
.stackViewGroup {
   display: grid;
   overflow: hidden;
}

.stackViewGroupContent {
   display: grid;
   width: 200%;
   height: 100%;
   grid-area: 1 / 1;
   grid-template-columns: 50% 50%;
}

.stackViewGroupView {
   position: sticky;
   top: 0;
   left: 0;

   display: grid;
   width: 100%;
   height: 100%;
   background-color: var(--canvas-background);
   transition-timing-function: cubic-bezier(0.7, 0, 0.05, 1);
   transition-property: translate, opacity;
   grid-area: 1 / 1;
   &:not(:first-child) {
      translate: 100%;
   }

   &:not([data-open]) {
      transition-duration: var(--stack-view-slide);
   }

   &:nth-last-child(1 of [data-open]) {
      transition-duration: var(--stack-view-slide);
      translate: 0;

      .stackViewGroup[data-dragging] &:not(:first-child) {
         --peek: calc(100% * (1 - var(--stack-view-group-pull-progress)));
         transition-duration: 0ms;
         translate: var(--peek);
      }
   }

   /* Select open views under the top view. */
   &[data-open]:not(:nth-last-child(1 of [data-open])) {
      opacity: 0;
      transition-duration: 0ms;
      pointer-events: none;
      /* Select the last open view. */
      &:nth-last-child(2 of [data-open]) {
         opacity: 0.75;
         transition-duration: calc(var(--stack-view-slide) * 1.5);
         translate: -20%;

         .stackViewGroup[data-dragging] & {
            --peek: calc(-20% * var(--stack-view-group-pull-progress));
            --opacity: calc(1 - var(--stack-view-group-pull-progress) + 0.5);
            opacity: var(--opacity);
            transition-duration: 0ms;
            translate: var(--peek);
            /* Mimics a brightness darken by using opacity on the before pseudo-element. */
            &::before {
               opacity: calc(0.4 * var(--stack-view-group-pull-progress));
               transition-duration: 0ms;
            }
         }
      }

      &::before {
         opacity: 0.4;
      }
   }
   & > * {
      grid-area: 1 / 1;
   }

   &::before {
      display: block;
      opacity: 0;
      background-color: black;
      content: "";
      transition-timing-function: inherit;
      transition-duration: var(--stack-view-slide);
      transition-property: opacity;
      pointer-events: none;
      grid-area: 1 / 1;
   }
}

.stackViewGroupViewPullHandle {
   display: none;

   .stackViewGroupView:nth-last-child(1 of [data-open]) > & {
      display: block;
      width: 10%;
      touch-action: none;
      overscroll-behavior: none;
   }
}
