/*
   NOTE: The component styles are imported here and not
   in the components themselves so that Tailwind
   can resolve the utilities.
*/
@import "./animations.css";

@source "./";

@font-face {
   font-family: "Instrument Sans Regular";
   font-style: normal;
   font-weight: 500;
   font-display: swap;
   src: url(../fonts/instrument-sans.ttf) format("truetype");
}
@font-face {
   font-family: "Instrument Sans Italic O";
   font-style: italic;
   font-weight: 500;
   font-display: swap;
   src: url(../fonts/instrument-sans-italic.ttf) format("truetype");
   unicode-range: U+006F;
}

@property --scroll-unit {
   syntax: "<number>";
   initial-value: 0;
   inherits: true;
}

:root {
   --animation-scrolling: scrolling;
   font-size: 16px;
   scrollbar-width: none;
}

::selection {
   background-color: rgb(250, 255, 237, 0.3);
}

* {
   scrollbar-width: none;

   &::-webkit-scrollbar {
      display: none;
   }
}

body {
   font-family: "Instrument Sans Italic O", "Instrument Sans Regular",
      sans-serif;
   @apply dark-scheme;
}

@theme {
   --spacing: 25px;
   --half-spacing: calc(var(--spacing) / 2);

   --color-base: rgb(39, 39, 39);
   --color-light-yellow: rgb(250, 255, 237);
   --color-white: rgb(255, 255, 255);
   --color-amaranth: rgb(211, 52, 67);
   --color-canvas: var(--canvas);
   --color-canvas-background: var(--canvas-background);
   --color-canvas-text: var(--canvas-text);

   --text-smallest: 0.5rem;
   --text-smaller: 0.625rem;
   --text-small: 0.75rem;
   --text-normal: 0.9375rem;
   --text-big: 1.0625rem;
   --text-bigger: 1.5rem;
   --text-header: 1.35625rem;
   --text-title: 1.875rem;
   --text-large: 3.125rem;
   --text-logo: 4.0625rem;

   --default-speed: 150ms;

   --speed-default: var(--default-speed);
   --speed-faster: calc(var(--default-speed) * 0.5);
   --speed-fast: calc(var(--default-speed) * 0.75);
   --speed-slow: calc(var(--default-speed) * 2);
   --speed-bit-slower: calc(var(--default-speed) * 2.5);
   --speed-slower: calc(var(--default-speed) * 4.5);
   --speed-much-slower: calc(var(--default-speed) * 6);
   --speed-slowest: calc(var(--default-speed) * 8);

   --animate-spin: spin var(--speed-slowest) linear infinite;
   --animate-fade-in: fade-in var(--speed-fast) ease-in backwards;
   --animate-underlining: underlining var(--speed-slow) ease-in-out
      var(--speed-slow) backwards;

   --toast-slide-in: toast-slide-in var(--speed-slow) ease;
   --fab-size: max(12dvh, 50px);
}

@layer base {
   p,
   span {
      @apply text-normal;
   }

   button {
      &:disabled {
         visibility: hidden;
      }

      @apply text-canvas bg-canvas-text border-3 border-canvas;
      @apply rounded-(--half-spacing) whitespace-nowrap px-0.5 py-0.5 text-normal min-w-[min(calc(var(--spacing)*10),30dvw)];
      @apply active:scale-[95%] duration-default transition-[scale] active:ease-out not-[active]:ease-in;
      @apply has-[.btn-icon]:flex has-[.btn-icon]:items-center has-[.btn-icon]:justify-center has-[.btn-icon]:gap-0.125;
   }

   input {
      @apply border-b-3 min-h-2 text-canvas-text w-full;
   }

   select {
      @apply border-b-3 min-h-2;
      @apply [&>option]:bg-canvas-background [&>option]:text-canvas-text [&>option]:hover:text-canvas;

      @supports (appearance: base-select) {
         &::picker-icon {
            content: var(--caret);
         }

         @apply flex items-center [appearance:base-select];

         ::picker(select) {
            border: none;
         }
      }
   }

   label:has(input[type="checkbox" i]) {
      &:has(:checked) {
         @apply before:translate-x-[150%];
      }

      &:not(:has(:checked)) {
         @apply after:opacity-60 before:opacity-60;
      }

      @apply grid grid-cols-[auto_auto] items-center justify-between min-h-2;
      @apply before:[grid-area:1/2] before:bg-canvas-text before:w-[calc(var(--text-normal)*0.55)] before:h-[calc(var(--text-normal)*0.55)] before:rounded-[50%] before:[margin-inline-start:15%];
      @apply after:[grid-area:1/2] after:w-[calc(var(--text-normal)*2)] after:h-[calc(var(--text-normal)*1.25)] after:border-3 after:rounded-xl;
      @apply before:transition-transform after:transition-opacity before:duration-default after:duration-default before:ease-in-out after:ease-in-out;

      input[type="checkbox"] {
         @apply appearance-none w-0 h-0 [grid-area:1/2];
      }
   }

   a {
      @apply grid py-0.75 border-b-2 w-full;
      @apply has-[.link-icon]:flex has-[.link-icon]:items-center has-[.link-icon]:gap-0.125;
   }
}

@utility light-scheme {
   --grid-lines-color: rgb(39, 39, 39, 0.05);
   --grid-bg-color: white;
   --caret: url("./svg/caret-light.svg");

   --canvas-text: var(--color-base);
   --canvas: var(--color-light-yellow);
   --canvas-background: white;
   /*
      Note: The three variables below should ideally inherit
      the color of the canvas as already defined in @theme,
      but they become undefined for some reason.
    */
   --color-canvas: var(--canvas);
   --color-canvas-background: var(--canvas-background);
   --color-canvas-text: var(--canvas-text);

   color: var(--color-base);
   background-color: white;

   input,
   select {
      color-scheme: light;
      accent-color: var(--color-base);
   }
}

@utility dark-scheme {
   --grid-lines-color: rgb(250, 255, 237, 0.05);
   --grid-bg-color: var(--color-base);
   --caret: url("./svg/caret-dark.svg");

   --canvas-text: var(--color-light-yellow);
   --canvas: var(--color-base);
   --canvas-background: var(--color-base);
   /* Note: see .light-theme */
   --color-canvas: var(--canvas);
   --color-canvas-background: var(--canvas-background);
   --color-canvas-text: var(--canvas-text);

   color: var(--color-light-yellow);
   background-color: var(--color-base);

   input,
   select {
      color-scheme: dark;
      accent-color: var(--color-light-yellow);
   }
}

@utility gap-0.125 {
   gap: calc(var(--spacing) * 0.125);
}

@utility h-0.15 {
   height: calc(var(--spacing) * 0.15);
}

@utility h-screen {
   height: 100dvh;
}

@utility w-screen {
   width: 100dvw;
}

@utility grid-lines {
   --grid-lines-stroke: 2px;
   background-color: var(--color-base);
   background-color: var(--grid-bg-color);
   background-image: linear-gradient(
         var(--grid-lines-color) var(--grid-lines-stroke),
         transparent var(--grid-lines-stroke)
      ),
      linear-gradient(
         90deg,
         var(--grid-lines-color) var(--grid-lines-stroke),
         transparent var(--grid-lines-stroke)
      );
   background-position: calc(-1 * var(--grid-lines-stroke))
      calc(-1 * var(--grid-lines-stroke));
   background-size: 50px 50px; /* Adjust grid size */
}

@utility button-bare {
   @apply border-none px-0 justify-start flex bg-transparent text-canvas-text;
   @apply active:scale-100;
}

@utility btn-danger {
   @apply text-light-yellow bg-amaranth;
}

@utility btn-outline {
   @apply bg-transparent text-canvas-text border-canvas-text;
}

@utility btn-danger-outline {
   @apply text-amaranth bg-transparent border-3 border-amaranth;
}

@utility btn-icon {
   @apply w-[calc(var(--text-normal)*1.5)] h-[calc(var(--text-normal)*1.5)];
}

@utility link-icon {
   @apply w-1 h-1;
}

@utility text-logo {
   letter-spacing: -3px;
}

@utility no-scrollbar {
   scrollbar-width: none;
   &::-webkit-scrollbar {
      display: none;
   }
}

@custom-variant expanded-ctx {
   &:where([data-expanded-ctx] &) {
      @slot;
   }
}
