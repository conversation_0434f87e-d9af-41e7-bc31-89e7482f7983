/// <reference types="node" />

import fs from 'node:fs/promises';
import path from 'node:path';

const iconsDir = path.resolve('./packages/components/icons/svg');
// Change output file extension to .tsx
const outputFile = path.resolve('./packages/components/icons/index.tsx');

async function generateIconNames() {
   try {
      const files = await fs.readdir(iconsDir);
      const iconNames = files
         .filter((file) => file.endsWith('.tsx') && file !== 'index.tsx') // Exclude index.tsx if present
         .map((file) => path.basename(file, '.tsx'));

      if (iconNames.length === 0) {
         // biome-ignore lint/suspicious/noConsole: script.
         console.log('No icon components found in', iconsDir);
         return;
      }

      // Sort names alphabetically for consistency
      iconNames.sort();

      // IconName type definition
      const typeContent = `export type IconName =
  | '${iconNames.join("'\n  | '")}';`;

      // AsyncIconProps interface definition (extracted from icon.tsx)
      const iconPropsContent = `
import type { JSX } from "retend/jsx-runtime";
import { noHydrate } from "retend-server/client";

type SvgProps = JSX.IntrinsicElements["svg"];

export interface IconProps extends SvgProps {}

export interface AsyncIconProps extends SvgProps {
   name: IconName;
   direction?: unknown;
}`;

      // Icon component definition (extracted from icon.tsx)
      const iconComponentContent = `
export async function DynamicIcon(props: AsyncIconProps) {
   const { name, ...rest } = props;
   const iconModule = await import(\`./svg/\${name}.tsx\`);
   return iconModule.default(rest) as JSX.Template;
}

export const Icon = noHydrate(DynamicIcon);`;

      // AllIcons component definition
      const allIconsComponentContent = `
export function AllIcons() {
  return (
    <>
      ${iconNames.map((name) => `<Icon name='${name}' />`).join('\n      ')}
    </>
  );
}`;

      // Combine all parts
      const fileContent = `// This file is auto-generated by scripts/generate-icon-names.mjs
// Do not edit this file manually.

${typeContent}
${iconPropsContent}
${iconComponentContent}
${allIconsComponentContent}
`;

      await fs.writeFile(outputFile, fileContent, 'utf8');
      // biome-ignore lint/suspicious/noConsole: script.
      console.log(
         `Successfully generated ${outputFile} with ${iconNames.length} icon names and components.`,
      );
   } catch (error) {
      // biome-ignore lint/suspicious/noConsole: script.
      console.error('Error generating icon names:', error);
      process.exit(1);
   }
}

generateIconNames();
