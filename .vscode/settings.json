{"explorer.compactFolders": false, "scm.compactFolders": true, "editor.defaultFormatter": "biomejs.biome", "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "cSpell.words": ["actiontriggered", "retend", "thresholdreached"], "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[svg]": {"editor.defaultFormatter": "jock.svg"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "typescript.experimental.useTsgo": false}